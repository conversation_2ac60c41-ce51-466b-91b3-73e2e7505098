import 'package:dandanplay_flutter/service/configure.dart';
import 'package:dandanplay_flutter/service/file_explorer.dart';
import 'package:dandanplay_flutter/service/media_library.dart';
import 'package:dandanplay_flutter/service/player/global.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'storage.dart';
import 'history.dart';

/// 服务定位器配置
/// 管理所有服务的依赖注入
class ServiceLocator {
  /// 初始化所有服务
  static Future<void> initialize() async {
    StorageService ss = await StorageService.register();
    SharedPreferences p = await SharedPreferences.getInstance();
    await MediaLibraryService.register(ss);
    await ConfigureService.register(p);
    await GlobalPlayerService.register();
    FileExplorerService.register();
    await HistoryService.register(ss);
  }
}
