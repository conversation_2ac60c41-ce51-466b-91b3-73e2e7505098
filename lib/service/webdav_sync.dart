import 'dart:convert';
import 'dart:io';
import 'package:dandanplay_flutter/model/sync_history_data.dart';
import 'package:dandanplay_flutter/service/configure.dart';
import 'package:dandanplay_flutter/service/history.dart';
import 'package:dandanplay_flutter/service/storage.dart';
import 'package:drift/drift.dart';
import 'package:flutter/foundation.dart';
import 'package:get_it/get_it.dart';
import 'package:path_provider/path_provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:signals_flutter/signals_flutter.dart';
import 'package:webdav_client_plus/webdav_client_plus.dart';

/// 合并历史记录结果
class _MergeResult {
  final List<History> mergedHistories;
  final int syncedCount;
  final int conflictCount;

  _MergeResult({
    required this.mergedHistories,
    required this.syncedCount,
    required this.conflictCount,
  });
}

/// WebDAV同步服务
class WebDAVSyncService {
  static const String _syncFileName = 'dandanplay_history.json';
  static const String _syncFolderPath = '/dandanplay';
  static const String _lastSyncTimeKey = 'webdav_last_sync_time';

  final ConfigureService _configureService;
  final HistoryService _historyService;
  final SharedPreferences _prefs;

  // 同步状态信号
  final Signal<SyncStatus> syncStatus = Signal(SyncStatus.idle);
  final Signal<String?> syncMessage = Signal(null);

  WebdavClient? _client;

  WebDAVSyncService({
    required ConfigureService configureService,
    required HistoryService historyService,
    required SharedPreferences prefs,
  }) : _configureService = configureService,
       _historyService = historyService,
       _prefs = prefs;

  /// 注册服务
  static Future<void> register(
    ConfigureService configureService,
    HistoryService historyService,
    SharedPreferences prefs,
  ) async {
    final service = WebDAVSyncService(
      configureService: configureService,
      historyService: historyService,
      prefs: prefs,
    );
    await service._initialize();
    GetIt.I.registerSingleton<WebDAVSyncService>(service);
  }

  /// 初始化服务
  Future<void> _initialize() async {
    _updateWebDAVClient(
      _configureService.webDavURL.value,
      _configureService.webDavUsername.value,
      _configureService.webDavPassword.value,
    );
    effect(() {
      _updateWebDAVClient(
        _configureService.webDavURL.value,
        _configureService.webDavUsername.value,
        _configureService.webDavPassword.value,
      );
    });
  }

  /// 更新WebDAV客户端
  void _updateWebDAVClient(String url, String username, String password) {
    if (url.isEmpty) {
      _client = null;
      return;
    }

    try {
      if (username.isEmpty || password.isEmpty) {
        _client = WebdavClient.noAuth(url: url);
      } else {
        _client = WebdavClient.basicAuth(
          url: url,
          user: username,
          pwd: password,
        );
      }
    } catch (e) {
      debugPrint('创建WebDAV客户端失败: $e');
      _client = null;
    }
  }

  /// 测试WebDAV连接
  Future<bool> testConnection() async {
    if (_client == null) {
      syncMessage.value = 'WebDAV客户端未配置';
      return false;
    }

    try {
      syncMessage.value = '测试连接中...';
      await _client!.readDir('/');
      syncMessage.value = '连接测试成功';
      return true;
    } catch (e) {
      syncMessage.value = '连接测试失败: $e';
      return false;
    }
  }

  /// 检查同步是否可用
  bool get canSync {
    return _configureService.syncEnable.value && _client != null;
  }

  /// 获取上次同步时间
  int get lastSyncTime {
    return _prefs.getInt(_lastSyncTimeKey) ?? 0;
  }

  /// 设置上次同步时间
  Future<void> _setLastSyncTime(int timestamp) async {
    await _prefs.setInt(_lastSyncTimeKey, timestamp);
  }

  /// 执行完整同步
  Future<SyncResult> syncHistories() async {
    if (!canSync) {
      return SyncResult.failure('同步功能未启用或配置不完整');
    }

    try {
      syncStatus.value = SyncStatus.syncing;
      syncMessage.value = '开始同步...';

      // 1. 测试连接
      syncMessage.value = '测试连接...';
      if (!await testConnection()) {
        syncStatus.value = SyncStatus.failed;
        return SyncResult.failure('WebDAV连接失败');
      }

      // 2. 确保远程目录存在
      syncMessage.value = '检查远程目录...';
      await _ensureRemoteDirectoryExists();

      // 3. 下载远程历史记录
      syncMessage.value = '下载远程历史记录...';
      final remoteData = await _downloadRemoteHistories();
      // 4. 获取本地历史记录
      syncMessage.value = '获取本地历史记录...';
      final localHistories = await _historyService.getAllHistories();

      // 5. 合并历史记录
      syncMessage.value = '合并历史记录...';
      final mergeResult = await _mergeHistories(localHistories, remoteData);

      // 6. 更新本地数据库
      syncMessage.value = '更新本地数据库...';
      await _updateLocalHistories(mergeResult.mergedHistories);

      // 7. 上传合并后的历史记录
      syncMessage.value = '上传历史记录...';
      await _uploadHistories(mergeResult.mergedHistories);
      debugPrint('成功上传 ${mergeResult.mergedHistories.length} 条历史记录');

      // 8. 更新同步时间
      final now = DateTime.now().millisecondsSinceEpoch;
      await _setLastSyncTime(now);

      syncStatus.value = SyncStatus.success;
      syncMessage.value = '同步完成';

      return SyncResult.success(
        syncedCount: mergeResult.syncedCount,
        conflictCount: mergeResult.conflictCount,
      );
    } catch (e) {
      debugPrint('同步失败: $e');
      syncStatus.value = SyncStatus.failed;
      syncMessage.value = '同步失败: $e';
      return SyncResult.failure(e.toString());
    }
  }

  /// 确保远程目录存在
  Future<void> _ensureRemoteDirectoryExists() async {
    try {
      await _client!.readDir(_syncFolderPath);
    } catch (e) {
      // 目录不存在，创建它
      try {
        await _client!.mkdir(_syncFolderPath);
      } catch (createError) {
        debugPrint('创建远程目录失败: $createError');
        // 忽略错误，可能目录已存在
      }
    }
  }

  /// 下载远程历史记录（使用文件下载方式）
  Future<SyncHistoryData?> _downloadRemoteHistories() async {
    debugPrint('开始下载远程历史记录');
    try {
      // 从WebDAV服务器下载文件
      final remotePath = '$_syncFolderPath/$_syncFileName';
      debugPrint('从远程路径下载: $remotePath');
      final fileContent = await _client!.read(remotePath);
      // 读取并解析文件内容
      final jsonString = utf8.decode(fileContent);
      final syncData = SyncHistoryData.fromJsonString(jsonString);
      debugPrint('成功解析远程历史记录，共 ${syncData.histories.length} 条');
      return syncData;
    } catch (e) {
      debugPrint('下载远程历史记录失败: $e');
      return null; // 文件不存在或读取失败
    }
  }

  /// 上传历史记录到远程
  Future<void> _uploadHistories(List<History> histories) async {
    debugPrint('开始上传历史记录，共 ${histories.length} 条');

    final syncData = SyncHistoryData(
      lastSyncTime: DateTime.now().millisecondsSinceEpoch,
      version: 1,
      histories: histories,
    );

    // 创建临时文件
    final tempDir = await getTemporaryDirectory();
    final tempFile = File('${tempDir.path}/$_syncFileName');
    debugPrint('创建临时文件: ${tempFile.path}');

    try {
      // 将数据写入临时文件
      final jsonString = syncData.toJsonString();
      await tempFile.writeAsString(jsonString, encoding: utf8);

      final fileSize = await tempFile.length();
      debugPrint('临时文件大小: $fileSize bytes');

      // 上传文件到WebDAV服务器
      final remotePath = '$_syncFolderPath/$_syncFileName';
      debugPrint('上传文件到: $remotePath');
      await _client!.writeFile('${tempDir.path}/$_syncFileName', remotePath);
      debugPrint('文件上传成功');
    } finally {
      // 清理临时文件
      if (await tempFile.exists()) {
        await tempFile.delete();
        debugPrint('临时文件已清理');
      }
    }
  }

  /// 合并本地和远程历史记录
  Future<_MergeResult> _mergeHistories(
    List<History> localHistories,
    SyncHistoryData? remoteData,
  ) async {
    final Map<String, History> mergedMap = {};
    int syncedCount = 0;
    int conflictCount = 0;

    // 添加本地历史记录
    for (final history in localHistories) {
      mergedMap[history.uniqueKey] = history;
    }

    // 合并远程历史记录
    if (remoteData != null) {
      for (final remoteHistory in remoteData.histories) {
        final localHistory = mergedMap[remoteHistory.uniqueKey];

        if (localHistory == null) {
          // 远程有，本地没有，直接添加
          mergedMap[remoteHistory.uniqueKey] = remoteHistory;
          syncedCount++;
        } else {
          // 两边都有，比较更新时间
          if (remoteHistory.updateTime > localHistory.updateTime) {
            // 远程更新，使用远程版本
            mergedMap[remoteHistory.uniqueKey] = remoteHistory;
            conflictCount++;
          } else if (remoteHistory.updateTime < localHistory.updateTime) {
            // 本地更新，保持本地版本
            conflictCount++;
          }
          // 如果时间相同，保持本地版本
        }
      }
    }

    return _MergeResult(
      mergedHistories: mergedMap.values.toList(),
      syncedCount: syncedCount,
      conflictCount: conflictCount,
    );
  }

  /// 更新本地历史记录
  Future<void> _updateLocalHistories(List<History> histories) async {
    // 这里需要批量更新数据库
    // 由于当前的StorageService没有批量操作，我们需要逐个处理
    for (final history in histories) {
      try {
        final existing = await _historyService.getHistoryByUniqueKey(
          history.uniqueKey,
        );
        if (existing == null) {
          // 新记录，插入
          await _historyService.storage.createHistory(
            HistoriesCompanion.insert(
              uniqueKey: history.uniqueKey,
              duration: history.duration,
              position: history.position,
              url: Value(history.url),
              headers: Value(history.headers),
              type: history.type,
              mediaLibraryId: Value(history.mediaLibraryId),
              updateTime: history.updateTime,
            ),
          );
        } else if (existing.updateTime < history.updateTime) {
          // 需要更新
          await _historyService.storage.updateHistory(
            HistoriesCompanion(
              id: Value(existing.id),
              duration: Value(history.duration),
              position: Value(history.position),
              url: Value(history.url),
              headers: Value(history.headers),
              type: Value(history.type),
              mediaLibraryId: Value(history.mediaLibraryId),
              updateTime: Value(history.updateTime),
            ),
          );
        }
      } catch (e) {
        debugPrint('更新历史记录失败: ${history.uniqueKey}, $e');
      }
    }
  }
}
