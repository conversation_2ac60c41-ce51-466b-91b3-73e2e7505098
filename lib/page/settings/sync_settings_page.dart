import 'package:dandanplay_flutter/model/sync_history_data.dart';
import 'package:dandanplay_flutter/service/configure.dart';
import 'package:dandanplay_flutter/service/webdav_sync.dart';
import 'package:dandanplay_flutter/widget/settings/settings_section.dart';
import 'package:dandanplay_flutter/widget/settings/settings_tile.dart';
import 'package:flutter/material.dart';
import 'package:forui/forui.dart';
import 'package:get_it/get_it.dart';
import 'package:signals_flutter/signals_flutter.dart';

class SyncSettingsPage extends StatefulWidget {
  const SyncSettingsPage({super.key});

  @override
  State<SyncSettingsPage> createState() => _SyncSettingsPageState();
}

class _SyncSettingsPageState extends State<SyncSettingsPage> {
  void _showInputDialog({
    required BuildContext context,
    required String title,
    required String currentValue,
    required Function(String) onSave,
    bool password = false,
  }) {
    showFDialog(
      context: context,
      builder: (context, style, animation) {
        final controller = TextEditingController(text: currentValue);
        return FDialog(
          style: style.call,
          direction: Axis.horizontal,
          animation: animation,
          title: Text(title),
          body:
              password
                  ? FTextField.password(controller: controller, label: null)
                  : FTextField(controller: controller),
          actions: [
            FButton(
              style: FButtonStyle.outline(),
              onPress: () => Navigator.pop(context),
              child: const Text('取消'),
            ),
            FButton(
              onPress: () {
                onSave(controller.text);
                Navigator.pop(context);
              },
              child: const Text('保存'),
            ),
          ],
        );
      },
    );
  }

  /// 测试WebDAV连接
  Future<void> _testConnection() async {
    try {
      final syncService = GetIt.I<WebDAVSyncService>();
      final success = await syncService.testConnection();

      if (mounted) {
        showFToast(
          context: context,
          title: Text(success ? '连接测试成功' : '连接测试失败'),
        );
      }
    } catch (e) {
      if (mounted) {
        showFToast(context: context, title: Text('连接测试失败: $e'));
      }
    }
  }

  /// 执行同步
  Future<void> _performSync() async {
    try {
      final syncService = GetIt.I<WebDAVSyncService>();
      final result = await syncService.syncHistories();

      if (mounted) {
        showFToast(
          context: context,
          title: Text(
            result.success
                ? '同步成功: ${result.syncedCount}条记录'
                : '同步失败: ${result.error}',
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        showFToast(context: context, title: Text('同步失败: $e'));
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final configure = GetIt.I<ConfigureService>();
    final sync = GetIt.I<WebDAVSyncService>();

    return Scaffold(
      appBar: AppBar(scrolledUnderElevation: 0, title: const Text('同步设置')),
      body: Padding(
        padding: context.theme.scaffoldStyle.childPadding,
        child: ListView(
          children: [
            SettingsSection(
              children: [
                Watch((context) {
                  return SettingsTile.switchTile(
                    title: '启用 WebDAV 同步',
                    switchValue: configure.syncEnable.value,
                    onBoolChange: (value) {
                      configure.syncEnable.value = value;
                    },
                  );
                }),
              ],
            ),
            SettingsSection(
              title: '服务器信息',
              children: [
                Watch((context) {
                  return SettingsTile.simpleTile(
                    title: 'Webdav地址',
                    subtitle: configure.webDavURL.value,
                    onPress: () {
                      _showInputDialog(
                        context: context,
                        title: 'Webdav地址',
                        currentValue: configure.webDavURL.value,
                        onSave: (value) => configure.webDavURL.value = value,
                      );
                    },
                  );
                }),
                Watch((context) {
                  return SettingsTile.simpleTile(
                    title: 'Webdav用户名',
                    subtitle: configure.webDavUsername.value,
                    onPress: () {
                      _showInputDialog(
                        context: context,
                        title: 'Webdav用户名',
                        currentValue: configure.webDavUsername.value,
                        onSave:
                            (value) => configure.webDavUsername.value = value,
                      );
                    },
                  );
                }),
                Watch((context) {
                  return SettingsTile.simpleTile(
                    title: 'Webdav密码',
                    onPress: () {
                      _showInputDialog(
                        context: context,
                        title: 'Webdav密码',
                        password: true,
                        currentValue: configure.webDavPassword.value,
                        onSave:
                            (value) => configure.webDavPassword.value = value,
                      );
                    },
                  );
                }),
              ],
            ),
            SettingsSection(
              title: '同步操作',
              children: [
                SettingsTile.navigationTile(
                  title: '测试连接',
                  subtitle: '测试WebDAV服务器连接',
                  onPress: _testConnection,
                ),
                SettingsTile.navigationTile(
                  title: '立即同步',
                  subtitle: '同步播放历史记录',
                  onPress: _performSync,
                ),
                Watch((context) {
                  return SettingsTile.simpleTile(
                    title: '同步状态',
                    subtitle: _getSyncStatusText(sync.syncStatus.value),
                  );
                }),
              ],
            ),
            const SizedBox(height: 8),
            FAlert(
              title: Text(_getSyncStatusText(sync.syncStatus.value)),
              subtitle: Text(
                sync.syncMessage.value ?? '准备同步...',
                style: Theme.of(context).textTheme.bodyMedium,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 获取同步状态文本
  String _getSyncStatusText(SyncStatus status) {
    switch (status) {
      case SyncStatus.idle:
        return '空闲';
      case SyncStatus.syncing:
        return '同步中...';
      case SyncStatus.success:
        return '同步成功';
      case SyncStatus.failed:
        return '同步失败';
      case SyncStatus.conflict:
        return '有冲突';
    }
  }
}
