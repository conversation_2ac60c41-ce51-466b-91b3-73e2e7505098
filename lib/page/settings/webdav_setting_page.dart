import 'package:dandanplay_flutter/service/configure.dart';
import 'package:dandanplay_flutter/widget/settings/settings_section.dart';
import 'package:dandanplay_flutter/widget/settings/settings_tile.dart';
import 'package:flutter/material.dart';
import 'package:forui/forui.dart';
import 'package:get_it/get_it.dart';
import 'package:signals_flutter/signals_flutter.dart';

class WebdavSettingsPage extends StatelessWidget {
  const WebdavSettingsPage({super.key});

  void _showInputDialog({
    required BuildContext context,
    required String title,
    required String currentValue,
    required Function(String) onSave,
  }) {
    showFDialog(
      context: context,
      builder: (context, style, animation) {
        final controller = TextEditingController(text: currentValue);
        return FDialog(
          style: style.call,
          direction: Axis.horizontal,
          animation: animation,
          title: Text(title),
          body: FTextField(controller: controller),
          actions: [
            FButton(
              onPress: () => Navigator.pop(context),
              child: const Text('取消'),
            ),
            FButton(
              onPress: () {
                onSave(controller.text);
                Navigator.pop(context);
              },
              child: const Text('保存'),
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    final configure = GetIt.I<ConfigureService>();

    return Scaffold(
      appBar: AppBar(scrolledUnderElevation: 0, title: const Text('WebDAV 设置')),
      body: Padding(
        padding: context.theme.scaffoldStyle.childPadding,
        child: ListView(
          children: [
            SettingsSection(
              children: [
                Watch((context) {
                  return SettingsTile.switchTile(
                    title: '启用 WebDAV 同步',
                    switchValue: configure.syncEnable.value,
                    onBoolChange: (value) {
                      configure.syncEnable.value = value;
                    },
                  );
                }),
              ],
            ),
            SettingsSection(
              title: '服务器信息',
              children: [
                Watch((context) {
                  return SettingsTile.navigationTile(
                    title: 'Webdav地址',
                    subtitle: configure.webDavURL.value,
                    onPress: () {
                      _showInputDialog(
                        context: context,
                        title: 'Webdav地址',
                        currentValue: configure.webDavURL.value,
                        onSave: (value) => configure.webDavURL.value = value,
                      );
                    },
                  );
                }),
                Watch((context) {
                  return SettingsTile.navigationTile(
                    title: 'Webdav用户名',
                    subtitle: configure.webDavUsername.value,
                    onPress: () {
                      _showInputDialog(
                        context: context,
                        title: 'Webdav用户名',
                        currentValue: configure.webDavUsername.value,
                        onSave:
                            (value) => configure.webDavUsername.value = value,
                      );
                    },
                  );
                }),
                Watch((context) {
                  return SettingsTile.navigationTile(
                    title: 'Webdav密码',
                    onPress: () {
                      _showInputDialog(
                        context: context,
                        title: 'Webdav密码',
                        currentValue: configure.webDavPassword.value,
                        onSave:
                            (value) => configure.webDavPassword.value = value,
                      );
                    },
                  );
                }),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
