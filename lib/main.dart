import 'package:dandanplay_flutter/router.dart';
import 'package:dandanplay_flutter/service/configure.dart';
import 'package:dandanplay_flutter/service/service_locator.dart';
import 'package:dandanplay_flutter/utils/theme.dart';
import 'package:flutter/material.dart';
import 'package:flutter_volume_controller/flutter_volume_controller.dart';
import 'package:forui/forui.dart';
import 'package:get_it/get_it.dart';
import 'package:media_kit/media_kit.dart';
import 'package:signals_flutter/signals_flutter.dart';

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // register services
  SignalsObserver.instance = null;
  await ServiceLocator.initialize();
  MediaKit.ensureInitialized();
  await FlutterVolumeController.updateShowSystemUI(false);
  runApp(Application());
}

class Application extends StatefulWidget {
  const Application({super.key});

  @override
  State<Application> createState() => _ApplicationState();
}

class _ApplicationState extends State<Application> with WidgetsBindingObserver {
  _ApplicationState();
  final _isDark = signal(
    WidgetsBinding.instance.platformDispatcher.platformBrightness ==
        Brightness.dark,
  );
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void didChangePlatformBrightness() {
    _isDark.value =
        WidgetsBinding.instance.platformDispatcher.platformBrightness ==
        Brightness.dark;
  }

  @override
  Widget build(BuildContext context) {
    final configureService = GetIt.I.get<ConfigureService>();
    final themeMode = configureService.themeMode.watch(context);
    final themeColor = configureService.themeColor.watch(context);
    var materialThemeMode = ThemeMode.system;
    switch (themeMode) {
      case '0':
        materialThemeMode = ThemeMode.system;
        break;
      case '1':
        materialThemeMode = ThemeMode.light;
        break;
      case '2':
        materialThemeMode = ThemeMode.dark;
        break;
    }
    var fTheme = getLightTheme(themeColor);
    switch (themeMode) {
      case '0':
        fTheme =
            _isDark.value
                ? getDarkTheme(themeColor)
                : getLightTheme(themeColor);
        break;
      case '1':
        fTheme = getLightTheme(themeColor);
        break;
      case '2':
        fTheme = getDarkTheme(themeColor);
        break;
    }
    return MaterialApp.router(
      localizationsDelegates: FLocalizations.localizationsDelegates,
      supportedLocales: FLocalizations.supportedLocales,
      theme: getLightTheme(themeColor).toApproximateMaterialTheme(),
      darkTheme: getDarkTheme(themeColor).toApproximateMaterialTheme(),
      themeMode: materialThemeMode,
      builder:
          (context, child) =>
              FToaster(child: FTheme(data: fTheme, child: child!)),
      routerConfig: router,
    );
  }
}
